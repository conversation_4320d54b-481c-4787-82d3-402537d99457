{"@@locale": "en", "appTitle": "RailOps", "@appTitle": {"description": "The title of the application"}, "cancel": "Cancel", "@cancel": {"description": "Cancel button text"}, "confirm": "Confirm", "@confirm": {"description": "Confirm button text"}, "delete": "Delete", "@delete": {"description": "Delete button text"}, "edit": "Edit", "@edit": {"description": "Edit button text"}, "error": "Error", "@error": {"description": "Generic error message label"}, "home": "Home", "@home": {"description": "Home navigation label"}, "loading": "Loading...", "@loading": {"description": "Loading indicator text"}, "login": "<PERSON><PERSON>", "@login": {"description": "Login button text"}, "logout": "Logout", "@logout": {"description": "Logout button text"}, "next": "Next", "@next": {"description": "Next button text"}, "no": "No", "@no": {"description": "No button text"}, "ok": "OK", "@ok": {"description": "OK button text"}, "password": "Password", "@password": {"description": "Password field label"}, "previous": "Previous", "@previous": {"description": "Previous button text"}, "refresh": "Refresh", "@refresh": {"description": "Refresh button text"}, "retry": "Retry", "@retry": {"description": "Retry button text"}, "save": "Save", "@save": {"description": "Save button text"}, "search": "Search", "@search": {"description": "Search field placeholder or button text"}, "settings": "Settings", "@settings": {"description": "Settings navigation label"}, "submit": "Submit", "@submit": {"description": "Submit button text"}, "username": "Username", "@username": {"description": "Username field label"}, "welcome": "Welcome to RailOps", "@welcome": {"description": "Welcome message displayed on home screen"}, "yes": "Yes", "@yes": {"description": "Yes button text"}, "str_message": "message", "@str_message": {"description": "Text from general_strings: message", "context": "general_strings"}, "str_stations": "stations", "@str_stations": {"description": "Text from general_strings: stations", "context": "general_strings"}, "str_details": "details", "@str_details": {"description": "Text from general_strings: details", "context": "general_strings"}, "str_trainname": "train_name", "@str_trainname": {"description": "Text from general_strings: train_name", "context": "general_strings"}, "str_journeyto": "journey_to", "@str_journeyto": {"description": "Text from general_strings: journey_to", "context": "general_strings"}, "str_trains": "trains", "@str_trains": {"description": "Text from general_strings: trains", "context": "general_strings"}, "str_coaches": "coaches", "@str_coaches": {"description": "Text from general_strings: coaches", "context": "general_strings"}, "str_logincode": "login_code", "@str_logincode": {"description": "Text from general_strings: login_code", "context": "general_strings"}, "str_station": "station", "@str_station": {"description": "Text from general_strings: station", "context": "general_strings"}, "str_refreshtoken_1": "refreshToken", "@str_refreshtoken_1": {"description": "Text from general_strings: refreshToken", "context": "general_strings"}, "str_stationcode_1": "stationCode", "@str_stationcode_1": {"description": "Text from general_strings: stationCode", "context": "general_strings"}, "str_stationname_1": "stationName", "@str_stationname_1": {"description": "Text from general_strings: stationName", "context": "general_strings"}, "str_stationcategory_1": "stationCategory", "@str_stationcategory_1": {"description": "Text from general_strings: stationCategory", "context": "general_strings"}, "str_updatedby": "updated_by", "@str_updatedby": {"description": "Text from general_strings: updated_by", "context": "general_strings"}, "str_updatedat": "updated_at", "@str_updatedat": {"description": "Text from general_strings: updated_at", "context": "general_strings"}, "str_updatedat_1": "updatedAt", "@str_updatedat_1": {"description": "Text from general_strings: updatedAt", "context": "general_strings"}, "str_updatedby_1": "updatedBy", "@str_updatedby_1": {"description": "Text from general_strings: updatedBy", "context": "general_strings"}, "str_trainno": "train_no", "@str_trainno": {"description": "Text from general_strings: train_no", "context": "general_strings"}, "str_statusfor": "status_for", "@str_statusfor": {"description": "Text from general_strings: status_for", "context": "general_strings"}, "str_status": "status", "@str_status": {"description": "Text from general_strings: status", "context": "general_strings"}, "str_coachno": "coach_no", "@str_coachno": {"description": "Text from general_strings: coach_no", "context": "general_strings"}, "str_berthno": "berth_no", "@str_berthno": {"description": "Text from general_strings: berth_no", "context": "general_strings"}, "str_success": "success", "@str_success": {"description": "Text from general_strings: success", "context": "general_strings"}, "str_no_message_provided": "No message provided", "@str_no_message_provided": {"description": "Text from general_strings: No message provided", "context": "general_strings"}, "str_extrainfo": "extra_info", "@str_extrainfo": {"description": "Text from general_strings: extra_info", "context": "general_strings"}, "str_tostation": "to_station", "@str_tostation": {"description": "Text from general_strings: to_station", "context": "general_strings"}, "str_traintype": "train_type", "@str_traintype": {"description": "Text from general_strings: train_type", "context": "general_strings"}, "text_reenter_otp": "Re-enter OTP", "@text_reenter_otp": {"description": "Text from text_widgets: Re-enter OTP", "context": "text_widgets"}, "form_select_date": "Select Date", "@form_select_date": {"description": "Text from form_labels: Select Date", "context": "form_labels"}, "str_deleteipo": "deleteIPO", "@str_deleteipo": {"description": "Text from general_strings: deleteIPO", "context": "general_strings"}, "str_deleteuser": "deleteUser", "@str_deleteuser": {"description": "Text from general_strings: deleteUser", "context": "general_strings"}, "str_getuserdetailsbyid": "getUserDetailsById", "@str_getuserdetailsbyid": {"description": "Text from general_strings: getUserDetailsById", "context": "general_strings"}, "str_brokercontroller": "broker-controller/", "@str_brokercontroller": {"description": "Text from general_strings: broker-controller/", "context": "general_strings"}, "str_getallbrokers": "getAllBrokers", "@str_getallbrokers": {"description": "Text from general_strings: getAllBrokers", "context": "general_strings"}, "str_updatepgtransaction": "updatePGTransaction", "@str_updatepgtransaction": {"description": "Text from general_strings: updatePGTransaction", "context": "general_strings"}, "str_saveuserbankdetails": "saveUserBankDetails", "@str_saveuserbankdetails": {"description": "Text from general_strings: saveUserBankDetails", "context": "general_strings"}, "str_getuserbankdetails": "getUserBankDetails", "@str_getuserbankdetails": {"description": "Text from general_strings: getUserBankDetails", "context": "general_strings"}, "str_updatecontroller": "update-controller/", "@str_updatecontroller": {"description": "Text from general_strings: update-controller/", "context": "general_strings"}, "str_checkappupdate": "checkAppUpdate", "@str_checkappupdate": {"description": "Text from general_strings: checkAppUpdate", "context": "general_strings"}, "text_deny": "<PERSON><PERSON>", "@text_deny": {"description": "Text from text_widgets: <PERSON>y", "context": "text_widgets"}, "text_enable": "Enable", "@text_enable": {"description": "Text from text_widgets: Enable", "context": "text_widgets"}, "text_location_access_required": "Location Access Required", "@text_location_access_required": {"description": "Text from text_widgets: Location Access Required", "context": "text_widgets"}, "text_decline": "Decline", "@text_decline": {"description": "Text from text_widgets: Decline", "context": "text_widgets"}, "text_accept": "Accept", "@text_accept": {"description": "Text from text_widgets: Accept", "context": "text_widgets"}, "str_coach_attendent": "coach attendent", "@str_coach_attendent": {"description": "Text from general_strings: coach attendent", "context": "general_strings"}, "str_passenger": "Passenger", "@str_passenger": {"description": "Text from general_strings: Passenger", "context": "general_strings"}, "str_authtoken": "authToken", "@str_authtoken": {"description": "Text from general_strings: authToken", "context": "general_strings"}, "str_trainno_1": "trainNo", "@str_trainno_1": {"description": "Text from general_strings: trainNo", "context": "general_strings"}, "str_loginresponse": "loginResponse", "@str_loginresponse": {"description": "Text from general_strings: loginResponse", "context": "general_strings"}, "str_trainnumber_1": "trainNumber", "@str_trainnumber_1": {"description": "Text from general_strings: trainNumber", "context": "general_strings"}, "str_rootisolatetoken": "rootIsolateToken", "@str_rootisolatetoken": {"description": "Text from general_strings: rootIsolateToken", "context": "general_strings"}, "text_confirm_delete": "Confirm Delete", "@text_confirm_delete": {"description": "Text from text_widgets: Confirm Delete", "context": "text_widgets"}, "text_cancel": "Cancel", "@text_cancel": {"description": "Text from text_widgets: Cancel", "context": "text_widgets"}, "text_delete": "Delete", "@text_delete": {"description": "Text from text_widgets: Delete", "context": "text_widgets"}, "text_deleting": "Deleting...", "@text_deleting": {"description": "Text from text_widgets: Deleting...", "context": "text_widgets"}, "text_storage_permission_is": "Storage permission is required to download files", "@text_storage_permission_is": {"description": "Text from text_widgets: Storage permission is required to download files", "context": "text_widgets"}, "text_please_select_a": "Please select a train first", "@text_please_select_a": {"description": "Text from text_widgets: Please select a train first", "context": "text_widgets"}, "text_update_required": "Update Required", "@text_update_required": {"description": "Text from text_widgets: Update Required", "context": "text_widgets"}, "text_update_now": "Update Now", "@text_update_now": {"description": "Text from text_widgets: Update Now", "context": "text_widgets"}, "text_please_select_a_1": "Please select a train number and date.", "@text_please_select_a_1": {"description": "Text from text_widgets: Please select a train number and date.", "context": "text_widgets"}, "text_all_details_updated": "All details updated successfully.", "@text_all_details_updated": {"description": "Text from text_widgets: All details updated successfully.", "context": "text_widgets"}, "text_data_refreshed_successfully": "Data refreshed successfully", "@text_data_refreshed_successfully": {"description": "Text from text_widgets: Data refreshed successfully", "context": "text_widgets"}, "text_train_location_saved": "Train Location Saved Successfully", "@text_train_location_saved": {"description": "Text from text_widgets: Train Location Saved Successfully", "context": "text_widgets"}, "text_self": "Self", "@text_self": {"description": "Text from text_widgets: Self", "context": "text_widgets"}, "text_other_ca": "Other CA", "@text_other_ca": {"description": "Text from text_widgets: Other CA", "context": "text_widgets"}, "text_chart_has_not": "Chart has not been prepared for this station", "@text_chart_has_not": {"description": "Text from text_widgets: Chart has not been prepared for this station", "context": "text_widgets"}, "text_0": "] ?? 0}", "@text_0": {"description": "Text from text_widgets: ] ?? 0}", "context": "text_widgets"}, "text_0_1": "] ?? 0} - ", "@text_0_1": {"description": "Text from text_widgets: ] ?? 0} - ", "context": "text_widgets"}, "form_train_number": "Train Number", "@form_train_number": {"description": "Text from form_labels: Train Number", "context": "form_labels"}, "form_date": "Date", "@form_date": {"description": "Text from form_labels: Date", "context": "form_labels"}, "str_noemail": "noemail", "@str_noemail": {"description": "Text from general_strings: noemail", "context": "general_strings"}, "str_stationlist": "stationList", "@str_stationlist": {"description": "Text from general_strings: stationList", "context": "general_strings"}, "str_stationsdict": "stationsDict", "@str_stationsdict": {"description": "Text from general_strings: stationsDict", "context": "general_strings"}, "str_train_not_running": "Train Not Running", "@str_train_not_running": {"description": "Text from general_strings: Train Not Running", "context": "general_strings"}, "str_insidetrainnumber": "insideTrainNumber", "@str_insidetrainnumber": {"description": "Text from general_strings: insideTrainNumber", "context": "general_strings"}, "str_coachnumbers_1": "<PERSON><PERSON><PERSON><PERSON>", "@str_coachnumbers_1": {"description": "Text from general_strings: coach<PERSON><PERSON><PERSON>", "context": "general_strings"}, "str_location_not_fetched": "location not fetched", "@str_location_not_fetched": {"description": "Text from general_strings: location not fetched", "context": "general_strings"}, "str_train_depotdepot": "Train Depot:$depot", "@str_train_depotdepot": {"description": "Text from general_strings: Train Depot:$depot", "context": "general_strings"}, "str_attendance_1": "Attendance", "@str_attendance_1": {"description": "Text from general_strings: Attendance", "context": "general_strings"}, "str_station_name": "station name", "@str_station_name": {"description": "Text from general_strings: station name", "context": "general_strings"}, "str_update": "Update", "@str_update": {"description": "Text from general_strings: Update", "context": "general_strings"}, "str_a_attendancecount": "A: $attendanceCount", "@str_a_attendancecount": {"description": "Text from general_strings: A: $attendanceCount", "context": "general_strings"}, "str_back_to_ac": "Back to AC Coaches", "@str_back_to_ac": {"description": "Text from general_strings: Back to AC Coaches", "context": "general_strings"}, "str_show_sleeper_coaches": "Show Sleeper Coaches", "@str_show_sleeper_coaches": {"description": "Text from general_strings: Show Sleeper Coaches", "context": "general_strings"}, "str_sleeper_coaches": "Sleeper Coaches", "@str_sleeper_coaches": {"description": "Text from general_strings: Sleeper Coaches", "context": "general_strings"}, "str_ac_coaches": "AC coaches", "@str_ac_coaches": {"description": "Text from general_strings: AC coaches", "context": "general_strings"}, "text_camera": "Camera", "@text_camera": {"description": "Text from text_widgets: Camera", "context": "text_widgets"}, "text_gallery": "Gallery", "@text_gallery": {"description": "Text from text_widgets: Gallery", "context": "text_widgets"}, "text_attendance_marked_successfully": "Attendance marked successfully!", "@text_attendance_marked_successfully": {"description": "Text from text_widgets: Attendance marked successfully!", "context": "text_widgets"}, "text_failed_to_get": "Failed to get location: $e", "@text_failed_to_get": {"description": "Text from text_widgets: Failed to get location: $e", "context": "text_widgets"}, "text_error_fetching_images": "Error fetching images: $e", "@text_error_fetching_images": {"description": "Text from text_widgets: Error fetching images: $e", "context": "text_widgets"}, "text_attendance_already_submitted": "Attendance Already Submitted", "@text_attendance_already_submitted": {"description": "Text from text_widgets: Attendance Already Submitted", "context": "text_widgets"}, "text_back_to_all": "Back to all users", "@text_back_to_all": {"description": "Text from text_widgets: Back to all users", "context": "text_widgets"}, "text_pick_photo": "Pick Photo", "@text_pick_photo": {"description": "Text from text_widgets: Pick Photo", "context": "text_widgets"}, "text_retake_photo": "Retake Photo", "@text_retake_photo": {"description": "Text from text_widgets: Retake Photo", "context": "text_widgets"}, "text_submit": "Submit", "@text_submit": {"description": "Text from text_widgets: Submit", "context": "text_widgets"}, "text_upload_status": "Upload Status", "@text_upload_status": {"description": "Text from text_widgets: Upload Status", "context": "text_widgets"}, "text_compressing_image": "Compressing image", "@text_compressing_image": {"description": "Text from text_widgets: Compressing image", "context": "text_widgets"}, "text_close": "Close", "@text_close": {"description": "Text from text_widgets: Close", "context": "text_widgets"}, "text_image_uploading": "Image Uploading", "@text_image_uploading": {"description": "Text from text_widgets: Image Uploading", "context": "text_widgets"}, "str_uploaded_images": "Uploaded Images", "@str_uploaded_images": {"description": "Text from general_strings: Uploaded Images", "context": "general_strings"}, "str_no_images_uploaded": "No images uploaded", "@str_no_images_uploaded": {"description": "Text from general_strings: No images uploaded", "context": "general_strings"}, "str_active_uploads": "Active Uploads:", "@str_active_uploads": {"description": "Text from general_strings: Active Uploads:", "context": "general_strings"}, "str_upload_image": "Upload Image", "@str_upload_image": {"description": "Text from general_strings: Upload Image", "context": "general_strings"}, "str_train_number": "Train Number:", "@str_train_number": {"description": "Text from general_strings: Train Number:", "context": "general_strings"}, "str_journey_date": "Journey Date:", "@str_journey_date": {"description": "Text from general_strings: Journey Date:", "context": "general_strings"}, "str_station_code": "Station Code:", "@str_station_code": {"description": "Text from general_strings: Station Code:", "context": "general_strings"}, "str_station_name_1": "Station Name:", "@str_station_name_1": {"description": "Text from general_strings: Station Name:", "context": "general_strings"}, "text_no_attendance_found": "No attendance found.", "@text_no_attendance_found": {"description": "Text from text_widgets: No attendance found.", "context": "text_widgets"}, "text_no_data_available": "No data available.", "@text_no_data_available": {"description": "Text from text_widgets: No data available.", "context": "text_widgets"}, "str_attendance_details": "Attendance Details", "@str_attendance_details": {"description": "Text from general_strings: Attendance Details", "context": "general_strings"}, "str_unknown": "Unknown", "@str_unknown": {"description": "Text from general_strings: Unknown", "context": "general_strings"}, "text_show_more": "Show More", "@text_show_more": {"description": "Text from text_widgets: Show More", "context": "text_widgets"}, "text_show_less": "Show Less", "@text_show_less": {"description": "Text from text_widgets: Show Less", "context": "text_widgets"}, "str_all_trains": "All Trains", "@str_all_trains": {"description": "Text from general_strings: All Trains", "context": "general_strings"}, "str_trains_not_running": "Trains Not Running", "@str_trains_not_running": {"description": "Text from general_strings: Trains Not Running", "context": "general_strings"}, "str_train_trainno": "Train: $trainNo", "@str_train_trainno": {"description": "Text from general_strings: Train: $trainNo", "context": "general_strings"}, "str_reports": "Reports", "@str_reports": {"description": "Text from general_strings: Reports", "context": "general_strings"}, "str_daily_reports": "Daily Reports", "@str_daily_reports": {"description": "Text from general_strings: Daily Reports", "context": "general_strings"}, "str_attendance_report": "Attendance Report", "@str_attendance_report": {"description": "Text from general_strings: Attendance Report", "context": "general_strings"}, "str_train_report": "Train Report", "@str_train_report": {"description": "Text from general_strings: Train Report", "context": "general_strings"}, "str_monthly_reports": "Monthly Reports", "@str_monthly_reports": {"description": "Text from general_strings: Monthly Reports", "context": "general_strings"}, "form_select_date_ddmmmyyyy": "Select Date (DD-MMM-YYYY)", "@form_select_date_ddmmmyyyy": {"description": "Text from form_labels: Select Date (DD-MMM-YYYY)", "context": "form_labels"}, "text_download_started": "Download Started!", "@text_download_started": {"description": "Text from text_widgets: Download Started!", "context": "text_widgets"}, "text_download": "Download", "@text_download": {"description": "Text from text_widgets: Download", "context": "text_widgets"}, "text_get_in_email": "Get in Email", "@text_get_in_email": {"description": "Text from text_widgets: Get in Email", "context": "text_widgets"}, "str_pdf_downloads": "PDF Downloads", "@str_pdf_downloads": {"description": "Text from general_strings: PDF Downloads", "context": "general_strings"}, "str_download_complete": "Download Complete", "@str_download_complete": {"description": "Text from general_strings: Download Complete", "context": "general_strings"}, "str_success_1": "Success", "@str_success_1": {"description": "Text from general_strings: Success", "context": "general_strings"}, "str_download": "download", "@str_download": {"description": "Text from general_strings: download", "context": "general_strings"}, "text_could_not_launch": "Could not launch the link", "@text_could_not_launch": {"description": "Text from text_widgets: Could not launch the link", "context": "text_widgets"}, "text_phone": "phone", "@text_phone": {"description": "Text from text_widgets: phone", "context": "text_widgets"}, "text_permission_denied": "Permission Denied", "@text_permission_denied": {"description": "Text from text_widgets: Permission Denied", "context": "text_widgets"}, "text_requested_users": "Requested Users", "@text_requested_users": {"description": "Text from text_widgets: Requested Users", "context": "text_widgets"}, "text_end_date_cannot": "End date cannot be before start date", "@text_end_date_cannot": {"description": "Text from text_widgets: End date cannot be before start date", "context": "text_widgets"}, "text_update_user_details": "Update User Details", "@text_update_user_details": {"description": "Text from text_widgets: Update User Details", "context": "text_widgets"}, "text_request_for_update": "Request For Update User Details", "@text_request_for_update": {"description": "Text from text_widgets: Request For Update User Details", "context": "text_widgets"}, "str_passenger_1": "passenger", "@str_passenger_1": {"description": "Text from general_strings: passenger", "context": "general_strings"}, "str_update_user_details": "update user details", "@str_update_user_details": {"description": "Text from general_strings: update user details", "context": "general_strings"}, "str_no_users_found": "No users found", "@str_no_users_found": {"description": "Text from general_strings: No users found", "context": "general_strings"}, "str_status_1": "STATUS", "@str_status_1": {"description": "Text from general_strings: STATUS", "context": "general_strings"}, "str_error": "$error", "@str_error": {"description": "Text from general_strings: $error", "context": "general_strings"}, "str_user_details": "User Details", "@str_user_details": {"description": "Text from general_strings: User Details", "context": "general_strings"}, "str_user_status": "User Status", "@str_user_status": {"description": "Text from general_strings: User Status", "context": "general_strings"}, "str_update_status": "Update Status", "@str_update_status": {"description": "Text from general_strings: Update Status", "context": "general_strings"}, "text_i_dont_have": "I don't have an email", "@text_i_dont_have": {"description": "Text from text_widgets: I don't have an email", "context": "text_widgets"}, "text_information": "Information", "@text_information": {"description": "Text from text_widgets: Information", "context": "text_widgets"}, "form_first_name": "First Name *", "@form_first_name": {"description": "Text from form_labels: First Name *", "context": "form_labels"}, "form_enter_first_name": "Enter first name", "@form_enter_first_name": {"description": "Text from form_labels: Enter first name", "context": "form_labels"}, "form_middle_name_optional": "Middle Name (Optional)", "@form_middle_name_optional": {"description": "Text from form_labels: Middle Name (Optional)", "context": "form_labels"}, "form_enter_middle_name": "Enter middle name", "@form_enter_middle_name": {"description": "Text from form_labels: Enter middle name", "context": "form_labels"}, "form_last_name": "Last Name *", "@form_last_name": {"description": "Text from form_labels: Last Name *", "context": "form_labels"}, "form_enter_last_name": "Enter last name", "@form_enter_last_name": {"description": "Text from form_labels: Enter last name", "context": "form_labels"}, "form_phone_number": "Phone Number", "@form_phone_number": {"description": "Text from form_labels: Phone Number", "context": "form_labels"}, "form_whatsapp_number": "WhatsApp Number", "@form_whatsapp_number": {"description": "Text from form_labels: WhatsApp Number", "context": "form_labels"}, "form_enter_10digit_whatsapp": "Enter 10-digit WhatsApp number", "@form_enter_10digit_whatsapp": {"description": "Text from form_labels: Enter 10-digit WhatsApp number", "context": "form_labels"}, "str_validation_error": "Validation Error", "@str_validation_error": {"description": "Text from general_strings: Validation Error", "context": "general_strings"}, "str_personal_information": "Personal Information", "@str_personal_information": {"description": "Text from general_strings: Personal Information", "context": "general_strings"}, "str_contact_details": "Contact Details", "@str_contact_details": {"description": "Text from general_strings: Contact Details", "context": "general_strings"}, "str_account_settings": "Account <PERSON><PERSON>", "@str_account_settings": {"description": "Text from general_strings: Account <PERSON><PERSON>s", "context": "general_strings"}, "form_mobile_number": "Mobile Number *", "@form_mobile_number": {"description": "Text from form_labels: Mobile Number *", "context": "form_labels"}, "form_email": "Email *", "@form_email": {"description": "Text from form_labels: Email *", "context": "form_labels"}, "form_enter_your_email": "Enter your email address", "@form_enter_your_email": {"description": "Text from form_labels: Enter your email address", "context": "form_labels"}, "text_request_for_sign": "Request For Sign Up", "@text_request_for_sign": {"description": "Text from text_widgets: Request For Sign Up", "context": "text_widgets"}, "form_enter_your_first": "Enter your first name", "@form_enter_your_first": {"description": "Text from form_labels: Enter your first name", "context": "form_labels"}, "form_enter_your_middle": "Enter your middle name", "@form_enter_your_middle": {"description": "Text from form_labels: Enter your middle name", "context": "form_labels"}, "form_enter_your_last": "Enter your last name", "@form_enter_your_last": {"description": "Text from form_labels: Enter your last name", "context": "form_labels"}, "form_email_1": "Email", "@form_email_1": {"description": "Text from form_labels: Email", "context": "form_labels"}, "text_forgot_password": "Forgot Password", "@text_forgot_password": {"description": "Text from text_widgets: Forgot Password", "context": "text_widgets"}, "text_error": "Error", "@text_error": {"description": "Text from text_widgets: <PERSON><PERSON><PERSON>", "context": "text_widgets"}, "form_mobile_number_1": "Mobile Number", "@form_mobile_number_1": {"description": "Text from form_labels: Mobile Number", "context": "form_labels"}, "str_mobile_otp_login": "Mobile OTP Login", "@str_mobile_otp_login": {"description": "Text from general_strings: Mobile OTP Login", "context": "general_strings"}, "text_send_otp": "Send OTP", "@text_send_otp": {"description": "Text from text_widgets: Send OTP", "context": "text_widgets"}, "text_verify_otp": "Verify OTP", "@text_verify_otp": {"description": "Text from text_widgets: Verify OTP", "context": "text_widgets"}, "text_sign_in_with": "Sign in with Google", "@text_sign_in_with": {"description": "Text from text_widgets: Sign in with Google", "context": "text_widgets"}, "form_password": "Password *", "@form_password": {"description": "Text from form_labels: Password *", "context": "form_labels"}, "str_know_about_this": "Know about this app", "@str_know_about_this": {"description": "Text from general_strings: Know about this app", "context": "general_strings"}, "text_new_user_sign": "New User? Sign Up Here", "@text_new_user_sign": {"description": "Text from text_widgets: New User? Sign Up Here", "context": "text_widgets"}, "form_select_train_numbers": "Select Train Numbers", "@form_select_train_numbers": {"description": "Text from form_labels: Select Train Numbers", "context": "form_labels"}, "form_zone": "Zone", "@form_zone": {"description": "Text from form_labels: Zone", "context": "form_labels"}, "form_zone_1": "Zone *", "@form_zone_1": {"description": "Text from form_labels: Zone *", "context": "form_labels"}, "form_employee_id": "Employee Id *", "@form_employee_id": {"description": "Text from form_labels: Employee Id *", "context": "form_labels"}, "form_depot": "Depot *", "@form_depot": {"description": "Text from form_labels: Depot *", "context": "form_labels"}, "form_reenter_password": "Re-enter Password *", "@form_reenter_password": {"description": "Text from form_labels: Re-enter Password *", "context": "form_labels"}, "text_resend_otp": "Resend OTP", "@text_resend_otp": {"description": "Text from text_widgets: Resend OTP", "context": "text_widgets"}, "form_divisions": "Divisions", "@form_divisions": {"description": "Text from form_labels: Divisions", "context": "form_labels"}, "form_divisions_1": "Divisions *", "@form_divisions_1": {"description": "Text from form_labels: Divisions *", "context": "form_labels"}, "form_select_coaches": "Select Coaches", "@form_select_coaches": {"description": "Text from form_labels: Select Coaches", "context": "form_labels"}, "str_request_error": "Request Error", "@str_request_error": {"description": "Text from general_strings: Request Error", "context": "general_strings"}, "form_middle_name": "Middle Name", "@form_middle_name": {"description": "Text from form_labels: Middle Name", "context": "form_labels"}, "text_check_pnr_status": "Check PNR Status", "@text_check_pnr_status": {"description": "Text from text_widgets: Check PNR Status", "context": "text_widgets"}, "text_pnr_number": "PNR Number", "@text_pnr_number": {"description": "Text from text_widgets: PNR Number", "context": "text_widgets"}, "text_no_pnr_data": "No PNR Data Found", "@text_no_pnr_data": {"description": "Text from text_widgets: No PNR Data Found", "context": "text_widgets"}, "str_pnr_status": "PNR Status", "@str_pnr_status": {"description": "Text from general_strings: PNR Status", "context": "general_strings"}, "str_train_name": "Train Name", "@str_train_name": {"description": "Text from general_strings: Train Name", "context": "general_strings"}, "str_overall_status": "Overall Status", "@str_overall_status": {"description": "Text from general_strings: Overall Status", "context": "general_strings"}, "str_booking_date": "Booking Date", "@str_booking_date": {"description": "Text from general_strings: Booking Date", "context": "general_strings"}, "str_coach_berth": "Coach / <PERSON><PERSON>", "@str_coach_berth": {"description": "Text from general_strings: <PERSON> / <PERSON>h", "context": "general_strings"}, "text_your_current_location": "Your current location", "@text_your_current_location": {"description": "Text from text_widgets: Your current location", "context": "text_widgets"}, "form_select_train_number": "Select Train Number", "@form_select_train_number": {"description": "Text from form_labels: Select Train Number", "context": "form_labels"}, "text_location_services_disabled": "Location Services Disabled", "@text_location_services_disabled": {"description": "Text from text_widgets: Location Services Disabled", "context": "text_widgets"}, "text_location_permission_denied": "Location Permission Denied", "@text_location_permission_denied": {"description": "Text from text_widgets: Location Permission Denied", "context": "text_widgets"}, "text_open_settings": "Open Settings", "@text_open_settings": {"description": "Text from text_widgets: Open Settings", "context": "text_widgets"}, "text_no_location_data": "No location data available.", "@text_no_location_data": {"description": "Text from text_widgets: No location data available.", "context": "text_widgets"}, "text_no_data_available_1": "No data available", "@text_no_data_available_1": {"description": "Text from text_widgets: No data available", "context": "text_widgets"}, "str_train_details": "Train details", "@str_train_details": {"description": "Text from general_strings: Train details", "context": "general_strings"}, "str_passenger_chart": "Passenger Chart", "@str_passenger_chart": {"description": "Text from general_strings: Passenger Chart", "context": "general_strings"}, "str_station_1": "Station", "@str_station_1": {"description": "Text from general_strings: Station", "context": "general_strings"}, "text_no_train_details": "No train details available.", "@text_no_train_details": {"description": "Text from text_widgets: No train details available.", "context": "text_widgets"}, "str_inside_train": "inside train", "@str_inside_train": {"description": "Text from general_strings: inside train", "context": "general_strings"}, "str_coaches_1": "Coaches", "@str_coaches_1": {"description": "Text from general_strings: Coaches", "context": "general_strings"}, "str_train_details_1": "Train Details", "@str_train_details_1": {"description": "Text from general_strings: Train Details", "context": "general_strings"}, "str_add_trains": "Add Trains", "@str_add_trains": {"description": "Text from general_strings: Add Trains", "context": "general_strings"}, "text_none": "None", "@text_none": {"description": "Text from text_widgets: None", "context": "text_widgets"}, "str_no_users": "No Users", "@str_no_users": {"description": "Text from general_strings: No Users", "context": "general_strings"}, "str_coachinfo": "coachInfo", "@str_coachinfo": {"description": "Text from general_strings: coachInfo", "context": "general_strings"}, "str_coach_info": "Coach <PERSON><PERSON>", "@str_coach_info": {"description": "Text from general_strings: Coach In<PERSON>", "context": "general_strings"}, "text_download_pdf_for": "Download PDF for all stations", "@text_download_pdf_for": {"description": "Text from text_widgets: Download PDF for all stations", "context": "text_widgets"}, "text_mail_pdf_for": "Mail PDF for all stations", "@text_mail_pdf_for": {"description": "Text from text_widgets: Mail PDF for all stations", "context": "text_widgets"}, "str_no_users_onboarding_1": "No Users Onboarding", "@str_no_users_onboarding_1": {"description": "Text from general_strings: No Users Onboarding", "context": "general_strings"}, "form_select_stations": "Select Stations", "@form_select_stations": {"description": "Text from form_labels: Select Stations", "context": "form_labels"}, "str_not_inside_train": "Not inside train", "@str_not_inside_train": {"description": "Text from general_strings: Not inside train", "context": "general_strings"}, "str_summary": "Summary", "@str_summary": {"description": "Text from general_strings: Summary", "context": "general_strings"}, "str_station_code_1": "Station Code", "@str_station_code_1": {"description": "Text from general_strings: Station Code", "context": "general_strings"}, "form_whatsapp_number_1": "WhatsApp Number *", "@form_whatsapp_number_1": {"description": "Text from form_labels: WhatsApp Number *", "context": "form_labels"}, "form_enter_mobile_number": "Enter mobile number to fetch details", "@form_enter_mobile_number": {"description": "Text from form_labels: Enter mobile number to fetch details", "context": "form_labels"}, "str_staff_information": "Staff Information", "@str_staff_information": {"description": "Text from general_strings: Staff Information", "context": "general_strings"}, "str_loading_user_data": "Loading user data...", "@str_loading_user_data": {"description": "Text from general_strings: Loading user data...", "context": "general_strings"}, "str_update_user_profile": "Update User Profile", "@str_update_user_profile": {"description": "Text from general_strings: Update User Profile", "context": "general_strings"}, "str_contact_information": "Contact Information", "@str_contact_information": {"description": "Text from general_strings: Contact Information", "context": "general_strings"}, "str_role_information": "Role Information", "@str_role_information": {"description": "Text from general_strings: Role Information", "context": "general_strings"}, "str_update_user": "Update User", "@str_update_user": {"description": "Text from general_strings: Update User", "context": "general_strings"}, "form_enter_mobile_number_1": "Enter Mobile Number", "@form_enter_mobile_number_1": {"description": "Text from form_labels: Enter Mobile Number", "context": "form_labels"}, "str_user_data_not": "User data not found", "@str_user_data_not": {"description": "Text from general_strings: User data not found", "context": "general_strings"}, "form_related_train": "Related Train", "@form_related_train": {"description": "Text from form_labels: Related Train", "context": "form_labels"}, "form_division": "Division", "@form_division": {"description": "Text from form_labels: Division", "context": "form_labels"}, "form_depot_1": "Depot", "@form_depot_1": {"description": "Text from form_labels: Depot", "context": "form_labels"}, "form_charting_day": "Charting Day", "@form_charting_day": {"description": "Text from form_labels: Charting Day", "context": "form_labels"}, "form_from_station": "From Station", "@form_from_station": {"description": "Text from form_labels: From Station", "context": "form_labels"}, "form_to_station": "To Station", "@form_to_station": {"description": "Text from form_labels: To Station", "context": "form_labels"}, "form_start_time": "Start Time", "@form_start_time": {"description": "Text from form_labels: Start Time", "context": "form_labels"}, "form_eg_0900_am": "e.g. 09:00 AM", "@form_eg_0900_am": {"description": "Text from form_labels: e.g. 09:00 AM", "context": "form_labels"}, "form_end_time": "End Time", "@form_end_time": {"description": "Text from form_labels: End Time", "context": "form_labels"}, "form_eg_0500_pm": "e.g. 05:00 PM", "@form_eg_0500_pm": {"description": "Text from form_labels: e.g. 05:00 PM", "context": "form_labels"}, "form_charting_time": "Charting Time", "@form_charting_time": {"description": "Text from form_labels: Charting Time", "context": "form_labels"}, "str_train_frequency": "Train Frequency", "@str_train_frequency": {"description": "Text from general_strings: Train Frequency", "context": "general_strings"}, "str_yesterday": "yesterday", "@str_yesterday": {"description": "Text from general_strings: yesterday", "context": "general_strings"}, "text_add_configuration": "Add Configuration", "@text_add_configuration": {"description": "Text from text_widgets: Add Configuration", "context": "text_widgets"}, "text_select_charting_day": "Select Charting Day", "@text_select_charting_day": {"description": "Text from text_widgets: Select Charting Day", "context": "text_widgets"}, "form_return_gap_days": "Return Gap (Days)", "@form_return_gap_days": {"description": "Text from form_labels: Return Gap (Days)", "context": "form_labels"}, "form_related_train_number": "Related Train Number", "@form_related_train_number": {"description": "Text from form_labels: Related Train Number", "context": "form_labels"}, "form_train_type": "Train Type", "@form_train_type": {"description": "Text from form_labels: Train Type", "context": "form_labels"}, "str_yesterday_1": "Yesterday", "@str_yesterday_1": {"description": "Text from general_strings: Yesterday", "context": "general_strings"}, "str_add_train_details": "Add Train Details", "@str_add_train_details": {"description": "Text from general_strings: Add Train Details", "context": "general_strings"}, "str_coaches_sequence": "Coaches Sequence", "@str_coaches_sequence": {"description": "Text from general_strings: Coaches Sequence", "context": "general_strings"}, "text_submitting": "Submitting...", "@text_submitting": {"description": "Text from text_widgets: Submitting...", "context": "text_widgets"}, "text_return_gap_updated": "Return gap updated successfully", "@text_return_gap_updated": {"description": "Text from text_widgets: Return gap updated successfully", "context": "text_widgets"}, "text_data_not_refreshed": "Data not refreshed: $e", "@text_data_not_refreshed": {"description": "Text from text_widgets: Data not refreshed: $e", "context": "text_widgets"}, "text_edit_configuration": "Edit Configuration", "@text_edit_configuration": {"description": "Text from text_widgets: Edit Configuration", "context": "text_widgets"}, "form_search_train_number": "Search Train Number", "@form_search_train_number": {"description": "Text from form_labels: Search Train Number", "context": "form_labels"}, "str_edit_train_details": "Edit Train Details", "@str_edit_train_details": {"description": "Text from general_strings: Edit Train Details", "context": "general_strings"}, "form_coaches_comma_separated": "Coaches (comma separated)", "@form_coaches_comma_separated": {"description": "Text from form_labels: Coaches (comma separated)", "context": "form_labels"}, "form_eg_h_gsl": "E.g., H, GSL, A, B, M", "@form_eg_h_gsl": {"description": "Text from form_labels: E.g., H, GSL, A, B, M", "context": "form_labels"}, "form_enter_coach_names": "Enter coach names separated by commas", "@form_enter_coach_names": {"description": "Text from form_labels: Enter coach names separated by commas", "context": "form_labels"}, "form_select_days": "Select Days", "@form_select_days": {"description": "Text from form_labels: Select Days", "context": "form_labels"}, "form_add_stoppage": "Add Stoppage", "@form_add_stoppage": {"description": "Text from form_labels: Add Stoppage", "context": "form_labels"}, "form_type_a_station": "Type a station name and press Enter or Space", "@form_type_a_station": {"description": "Text from form_labels: Type a station name and press Enter or Space", "context": "form_labels"}, "form_search": "Search", "@form_search": {"description": "Text from form_labels: Search", "context": "form_labels"}, "form_type_a_station_1": "Type a Station and hit space or Enter", "@form_type_a_station_1": {"description": "Text from form_labels: Type a Station and hit space or Enter", "context": "form_labels"}, "form_frequency": "Frequency", "@form_frequency": {"description": "Text from form_labels: Frequency", "context": "form_labels"}, "text_no_coaches_available": "No coaches available", "@text_no_coaches_available": {"description": "Text from text_widgets: No coaches available", "context": "text_widgets"}, "form_enter_new_coach": "Enter new coach", "@form_enter_new_coach": {"description": "Text from form_labels: Enter new coach", "context": "form_labels"}, "form_use_comma_to": "Use comma to add multiple coaches", "@form_use_comma_to": {"description": "Text from form_labels: Use comma to add multiple coaches", "context": "form_labels"}, "str_delete": "delete", "@str_delete": {"description": "Text from general_strings: delete", "context": "general_strings"}, "str_addcoach": "add_coach", "@str_addcoach": {"description": "Text from general_strings: add_coach", "context": "general_strings"}, "text_please_select_a_2": "Please select a train and date first", "@text_please_select_a_2": {"description": "Text from text_widgets: Please select a train and date first", "context": "text_widgets"}, "text_coach_handover_report": "Coach Handover Report", "@text_coach_handover_report": {"description": "Text from text_widgets: Coach Handover Report", "context": "text_widgets"}, "str_no_coaches_found": "No coaches found.", "@str_no_coaches_found": {"description": "Text from general_strings: No coaches found.", "context": "general_strings"}, "text_name": "name", "@text_name": {"description": "Text from text_widgets: name", "context": "text_widgets"}, "text_save_selection": "Save Selection", "@text_save_selection": {"description": "Text from text_widgets: Save Selection", "context": "text_widgets"}, "text_select_media_type": "Select Media Type", "@text_select_media_type": {"description": "Text from text_widgets: Select Media Type", "context": "text_widgets"}, "text_image": "Image", "@text_image": {"description": "Text from text_widgets: Image", "context": "text_widgets"}, "text_video": "Video", "@text_video": {"description": "Text from text_widgets: Video", "context": "text_widgets"}, "text_please_select_images": "Please select images to upload", "@text_please_select_images": {"description": "Text from text_widgets: Please select images to upload", "context": "text_widgets"}, "text_images_uploaded_successfully": "Images uploaded successfully", "@text_images_uploaded_successfully": {"description": "Text from text_widgets: Images uploaded successfully", "context": "text_widgets"}, "text_failed_to_upload": "Failed to upload images", "@text_failed_to_upload": {"description": "Text from text_widgets: Failed to upload images", "context": "text_widgets"}, "text_error_updating_issue": "Error updating issue: $e", "@text_error_updating_issue": {"description": "Text from text_widgets: Error updating issue: $e", "context": "text_widgets"}, "text_statustype_by": "$statusType By", "@text_statustype_by": {"description": "Text from text_widgets: $statusType By", "context": "text_widgets"}, "text_no_images_available": "No images available", "@text_no_images_available": {"description": "Text from text_widgets: No images available", "context": "text_widgets"}, "text_status": "Status", "@text_status": {"description": "Text from text_widgets: Status", "context": "text_widgets"}, "text_submitupdate": "Submit/Update", "@text_submitupdate": {"description": "Text from text_widgets: Submit/Update", "context": "text_widgets"}, "text_pick_images": "Pick Images", "@text_pick_images": {"description": "Text from text_widgets: Pick Images", "context": "text_widgets"}, "text_uploading": "Uploading...", "@text_uploading": {"description": "Text from text_widgets: Uploading...", "context": "text_widgets"}, "text_submit_upload": "Submit & Upload", "@text_submit_upload": {"description": "Text from text_widgets: Submit & Upload", "context": "text_widgets"}, "str_reported": "Reported", "@str_reported": {"description": "Text from general_strings: Reported", "context": "general_strings"}, "str_no_issues": "No Issues", "@str_no_issues": {"description": "Text from general_strings: No Issues", "context": "general_strings"}, "str_revert_status": "Revert Status", "@str_revert_status": {"description": "Text from general_strings: Revert Status", "context": "general_strings"}, "str_train_number_1": "Train Number: ", "@str_train_number_1": {"description": "Text from general_strings: Train Number: ", "context": "general_strings"}, "str_coach": "Coach: ", "@str_coach": {"description": "Text from general_strings: Coach: ", "context": "general_strings"}, "str_journey_date_1": "Journey Date: ", "@str_journey_date_1": {"description": "Text from general_strings: Journey Date: ", "context": "general_strings"}, "str_no_images_selected": "No Images selected", "@str_no_images_selected": {"description": "Text from general_strings: No Images selected", "context": "general_strings"}, "text_retry": "Retry", "@text_retry": {"description": "Text from text_widgets: Retry", "context": "text_widgets"}, "str_request_summary": "Request Summary", "@str_request_summary": {"description": "Text from general_strings: Request Summary", "context": "general_strings"}, "str_update_requests": "Update Requests", "@str_update_requests": {"description": "Text from general_strings: Update Requests", "context": "general_strings"}, "str_no_pending_requests": "No Pending Requests", "@str_no_pending_requests": {"description": "Text from general_strings: No Pending Requests", "context": "general_strings"}, "text_approve": "Approve", "@text_approve": {"description": "Text from text_widgets: Approve", "context": "text_widgets"}, "str_no_pending_requests_1": "No pending requests", "@str_no_pending_requests_1": {"description": "Text from general_strings: No pending requests", "context": "general_strings"}, "text_no_requests_selected": "No requests selected", "@text_no_requests_selected": {"description": "Text from text_widgets: No requests selected", "context": "text_widgets"}, "text_confirm_denial": "Confirm Denial", "@text_confirm_denial": {"description": "Text from text_widgets: Confirm Denial", "context": "text_widgets"}, "text_deny_request": "<PERSON>y <PERSON>", "@text_deny_request": {"description": "Text from text_widgets: <PERSON><PERSON>", "context": "text_widgets"}, "text_approve_selected": "Approve Selected", "@text_approve_selected": {"description": "Text from text_widgets: Approve Selected", "context": "text_widgets"}, "text_approve_all": "Approve All", "@text_approve_all": {"description": "Text from text_widgets: Approve All", "context": "text_widgets"}, "text_processing_requests": "Processing requests...", "@text_processing_requests": {"description": "Text from text_widgets: Processing requests...", "context": "text_widgets"}, "text_clear_search": "Clear search", "@text_clear_search": {"description": "Text from text_widgets: Clear search", "context": "text_widgets"}, "form_search_by_name": "Search by name, email, phone or depot", "@form_search_by_name": {"description": "Text from form_labels: Search by name, email, phone or depot", "context": "form_labels"}, "str_approve_confirmation": "Approve Confirmation", "@str_approve_confirmation": {"description": "Text from general_strings: Approve Confirmation", "context": "general_strings"}, "str_profile_update": "Profile Update", "@str_profile_update": {"description": "Text from general_strings: Profile Update", "context": "general_strings"}, "str_no_fields_changed": "No fields changed", "@str_no_fields_changed": {"description": "Text from general_strings: No fields changed", "context": "general_strings"}, "text_failed_to_fetch_1": "Failed to fetch complaints", "@text_failed_to_fetch_1": {"description": "Text from text_widgets: Failed to fetch complaints", "context": "text_widgets"}, "text_error_fetching_trains": "Error fetching trains: $e", "@text_error_fetching_trains": {"description": "Text from text_widgets: Error fetching trains: $e", "context": "text_widgets"}, "text_other": "Other", "@text_other": {"description": "Text from text_widgets: Other", "context": "text_widgets"}, "text_existing_images": "Existing Images:", "@text_existing_images": {"description": "Text from text_widgets: Existing Images:", "context": "text_widgets"}, "text_delete_image": "Delete Image", "@text_delete_image": {"description": "Text from text_widgets: Delete Image", "context": "text_widgets"}, "text_image_deleted": "Image deleted", "@text_image_deleted": {"description": "Text from text_widgets: Image deleted", "context": "text_widgets"}, "text_newly_selected_images": "Newly Selected Images:", "@text_newly_selected_images": {"description": "Text from text_widgets: Newly Selected Images:", "context": "text_widgets"}, "text_add_image": "Add Image", "@text_add_image": {"description": "Text from text_widgets: Add Image", "context": "text_widgets"}, "text_complaint_updated": "<PERSON><PERSON><PERSON><PERSON> updated", "@text_complaint_updated": {"description": "Text from text_widgets: Complaint updated", "context": "text_widgets"}, "text_update_failed": "Update failed", "@text_update_failed": {"description": "Text from text_widgets: Update failed", "context": "text_widgets"}, "text_save_changes": "Save Changes", "@text_save_changes": {"description": "Text from text_widgets: Save Changes", "context": "text_widgets"}, "text_delete_complaint": "Delete Co<PERSON>t", "@text_delete_complaint": {"description": "Text from text_widgets: Delete Complaint", "context": "text_widgets"}, "text_are_you_sure_1": "Are you sure you want to delete this complaint?", "@text_are_you_sure_1": {"description": "Text from text_widgets: Are you sure you want to delete this complaint?", "context": "text_widgets"}, "text_complaint_deleted_successfully": "<PERSON><PERSON><PERSON><PERSON> deleted successfully", "@text_complaint_deleted_successfully": {"description": "Text from text_widgets: Complaint deleted successfully", "context": "text_widgets"}, "text_failed_to_delete": "Failed to delete complaint", "@text_failed_to_delete": {"description": "Text from text_widgets: Failed to delete complaint", "context": "text_widgets"}, "text_no_complaints_found": "No complaints found", "@text_no_complaints_found": {"description": "Text from text_widgets: No complaints found", "context": "text_widgets"}, "text_edit": "Edit", "@text_edit": {"description": "Text from text_widgets: Edit", "context": "text_widgets"}, "form_train": "Train", "@form_train": {"description": "Text from form_labels: Train", "context": "form_labels"}, "form_complaint_type": "Complaint Type", "@form_complaint_type": {"description": "Text from form_labels: Complaint Type", "context": "form_labels"}, "str_attemptedsuccess": "attempted-success", "@str_attemptedsuccess": {"description": "Text from general_strings: attempted-success", "context": "general_strings"}, "str_enter_train_number": "Enter Train Number", "@str_enter_train_number": {"description": "Text from general_strings: Enter Train Number", "context": "general_strings"}, "str_berth_no": "Berth No", "@str_berth_no": {"description": "Text from general_strings: <PERSON>h <PERSON>", "context": "general_strings"}, "text_complaint_submitted_successfully": "<PERSON><PERSON><PERSON><PERSON> submitted successfully!", "@text_complaint_submitted_successfully": {"description": "Text from text_widgets: Complaint submitted successfully!", "context": "text_widgets"}, "text_failed_to_submit": "Failed to submit complaint", "@text_failed_to_submit": {"description": "Text from text_widgets: Failed to submit complaint", "context": "text_widgets"}, "text_ehk_ehkdisplay": "EHK: $ehkDisplay", "@text_ehk_ehkdisplay": {"description": "Text from text_widgets: EHK: $ehkDisplay", "context": "text_widgets"}, "text_pending": "Pending", "@text_pending": {"description": "Text from text_widgets: Pending", "context": "text_widgets"}, "text_completed": "Completed", "@text_completed": {"description": "Text from text_widgets: Completed", "context": "text_widgets"}, "text_upload_imagevideo": "Upload Image/Video", "@text_upload_imagevideo": {"description": "Text from text_widgets: Upload Image/Video", "context": "text_widgets"}, "text_submit_issue": "Submit Issue", "@text_submit_issue": {"description": "Text from text_widgets: Submit Issue", "context": "text_widgets"}, "text_validate": "Validate", "@text_validate": {"description": "Text from text_widgets: Validate", "context": "text_widgets"}, "text_next": "Next", "@text_next": {"description": "Text from text_widgets: Next", "context": "text_widgets"}, "form_write_your_issue": "Write your issue", "@form_write_your_issue": {"description": "Text from form_labels: Write your issue", "context": "form_labels"}, "form_issue_status": "Issue Status", "@form_issue_status": {"description": "Text from form_labels: Issue Status", "context": "form_labels"}, "form_name": "Name", "@form_name": {"description": "Text from form_labels: Name", "context": "form_labels"}, "form_search_by_train": "Search by train number or name", "@form_search_by_train": {"description": "Text from form_labels: Search by train number or name", "context": "form_labels"}, "form_train_selection": "Train Selection", "@form_train_selection": {"description": "Text from form_labels: Train Selection", "context": "form_labels"}, "form_journey_start_date": "Journey Start Date", "@form_journey_start_date": {"description": "Text from form_labels: Journey Start Date", "context": "form_labels"}, "form_coach": "Coach", "@form_coach": {"description": "Text from form_labels: Coach", "context": "form_labels"}, "form_berth": "<PERSON><PERSON>", "@form_berth": {"description": "Text from form_labels: Berth", "context": "form_labels"}, "str_notattempted": "not-attempted", "@str_notattempted": {"description": "Text from general_strings: not-attempted", "context": "general_strings"}, "str_ehk_not_assigned": "EHK Not Assigned", "@str_ehk_not_assigned": {"description": "Text from general_strings: EHK Not Assigned", "context": "general_strings"}, "text_error_loading_authentication": "Error loading authentication state", "@text_error_loading_authentication": {"description": "Text from text_widgets: Error loading authentication state", "context": "text_widgets"}, "text_storage_permission_required": "Storage Permission Required", "@text_storage_permission_required": {"description": "Text from text_widgets: Storage Permission Required", "context": "text_widgets"}, "text_selection_cleared": "Selection cleared.", "@text_selection_cleared": {"description": "Text from text_widgets: Selection cleared.", "context": "text_widgets"}, "text_upload_json_data": "Upload Json Data", "@text_upload_json_data": {"description": "Text from text_widgets: Upload Json Data", "context": "text_widgets"}, "str_no_file_selected": "No file selected", "@str_no_file_selected": {"description": "Text from general_strings: No file selected", "context": "general_strings"}, "str_submit_file": "Submit File", "@str_submit_file": {"description": "Text from general_strings: Submit File", "context": "general_strings"}, "text_and_all_its": "]}\" and all its subissues?", "@text_and_all_its": {"description": "Text from text_widgets: ]}\" and all its subissues?", "context": "text_widgets"}, "text_": "]}\"?", "@text_": {"description": "Text from text_widgets: ]}\"?", "context": "text_widgets"}, "text_add_issue": "Add Issue", "@text_add_issue": {"description": "Text from text_widgets: Add Issue", "context": "text_widgets"}, "text_no_subissues": "No subissues", "@text_no_subissues": {"description": "Text from text_widgets: No subissues", "context": "text_widgets"}, "text_select_issue": "Select Issue", "@text_select_issue": {"description": "Text from text_widgets: Select Issue", "context": "text_widgets"}, "text_add_subissue": "Add Subissue", "@text_add_subissue": {"description": "Text from text_widgets: Add Subissue", "context": "text_widgets"}, "text_add_new_item": "Add New Item", "@text_add_new_item": {"description": "Text from text_widgets: Add New Item", "context": "text_widgets"}, "form_issue_name": "Issue Name", "@form_issue_name": {"description": "Text from form_labels: Issue Name", "context": "form_labels"}, "form_subissue_name": "Subissue Name", "@form_subissue_name": {"description": "Text from form_labels: Subissue Name", "context": "form_labels"}, "str_edit_issue": "Edit Issue", "@str_edit_issue": {"description": "Text from general_strings: Edit Issue", "context": "general_strings"}, "str_no_issues_found": "No issues found", "@str_no_issues_found": {"description": "Text from general_strings: No issues found", "context": "general_strings"}, "str_edit_issue_1": "Edit issue", "@str_edit_issue_1": {"description": "Text from general_strings: Edit issue", "context": "general_strings"}, "str_delete_issue": "Delete issue", "@str_delete_issue": {"description": "Text from general_strings: Delete issue", "context": "general_strings"}, "str_edit_subissue": "Edit Subissue", "@str_edit_subissue": {"description": "Text from general_strings: Edit Subissue", "context": "general_strings"}, "str_edit_subissue_1": "Edit subissue", "@str_edit_subissue_1": {"description": "Text from general_strings: Edit subissue", "context": "general_strings"}, "str_delete_subissue": "Delete subissue", "@str_delete_subissue": {"description": "Text from general_strings: Delete subissue", "context": "general_strings"}, "text_images_or_videos": "Images or Videos upload initiated successfully", "@text_images_or_videos": {"description": "Text from text_widgets: Images or Videos upload initiated successfully", "context": "text_widgets"}, "text_please_select_an": "Please select an image and issue to upload", "@text_please_select_an": {"description": "Text from text_widgets: Please select an image and issue to upload", "context": "text_widgets"}, "text_issues_saved_upload": "Issues saved upload Images/Videos", "@text_issues_saved_upload": {"description": "Text from text_widgets: Issues saved upload Images/Videos", "context": "text_widgets"}, "text_select_issues_for": "Select Issues for Coach $coach", "@text_select_issues_for": {"description": "Text from text_widgets: Select Issues for Coach $coach", "context": "text_widgets"}, "text_please_select_imagevideo": "Please select image/video to upload", "@text_please_select_imagevideo": {"description": "Text from text_widgets: Please select image/video to upload", "context": "text_widgets"}, "str_upload_imagesvideos": "Upload Images/Videos", "@str_upload_imagesvideos": {"description": "Text from general_strings: Upload Images/Videos", "context": "general_strings"}, "text_image_detail": "Image Detail", "@text_image_detail": {"description": "Text from text_widgets: Image Detail", "context": "text_widgets"}, "text_video_detail": "Video Detail", "@text_video_detail": {"description": "Text from text_widgets: Video Detail", "context": "text_widgets"}, "text_confirm_deletion": "Confirm Deletion", "@text_confirm_deletion": {"description": "Text from text_widgets: Confirm Deletion", "context": "text_widgets"}, "text_delete_report": "Delete Report", "@text_delete_report": {"description": "Text from text_widgets: Delete Report", "context": "text_widgets"}, "text_coach_issue_status": "Coach Issue Status", "@text_coach_issue_status": {"description": "Text from text_widgets: Coach Issue Status", "context": "text_widgets"}, "str_isssue_status": "Isssue Status", "@str_isssue_status": {"description": "Text from general_strings: Isssue Status", "context": "general_strings"}, "text_confirm": "Confirm", "@text_confirm": {"description": "Text from text_widgets: Confirm", "context": "text_widgets"}, "form_search_1": "Search...", "@form_search_1": {"description": "Text from form_labels: Search...", "context": "form_labels"}, "form_select_date_time": "Select Date & Time", "@form_select_date_time": {"description": "Text from form_labels: Select Date & Time", "context": "form_labels"}, "text_manage_issues": "Manage Issues", "@text_manage_issues": {"description": "Text from text_widgets: Manage Issues", "context": "text_widgets"}, "text_rake_deficiency_report": "Rake Deficiency Report Issues", "@text_rake_deficiency_report": {"description": "Text from text_widgets: Rake Deficiency Report Issues", "context": "text_widgets"}, "str_coach_coach": "Coach $coach", "@str_coach_coach": {"description": "Text from general_strings: Coach $coach", "context": "general_strings"}, "str_no_issues_reported": "No issues reported", "@str_no_issues_reported": {"description": "Text from general_strings: No issues reported", "context": "general_strings"}, "str_reported_issues": "Reported Issues:", "@str_reported_issues": {"description": "Text from general_strings: Reported Issues:", "context": "general_strings"}, "str_unknown_issue": "Unknown Issue", "@str_unknown_issue": {"description": "Text from general_strings: Unknown Issue", "context": "general_strings"}, "str_upload_coach_image": "Upload Coach Image", "@str_upload_coach_image": {"description": "Text from general_strings: Upload Coach Image", "context": "general_strings"}, "str_home_screen": "Home Screen", "@str_home_screen": {"description": "Text from general_strings: Home Screen", "context": "general_strings"}, "text_upload_pnr_image": "Upload PNR Image", "@text_upload_pnr_image": {"description": "Text from text_widgets: Upload PNR Image", "context": "text_widgets"}, "text_pick_imagesvideos_for": "Pick Images/Videos for Feedback", "@text_pick_imagesvideos_for": {"description": "Text from text_widgets: Pick Images/Videos for Feedback", "context": "text_widgets"}, "text_please_wait_until": "Please wait until the upload is complete", "@text_please_wait_until": {"description": "Text from text_widgets: Please wait until the upload is complete", "context": "text_widgets"}, "text_submit_feedback": "Submit <PERSON>", "@text_submit_feedback": {"description": "Text from text_widgets: Submit Feedback", "context": "text_widgets"}, "text_verify_email": "<PERSON><PERSON><PERSON>", "@text_verify_email": {"description": "Text from text_widgets: Verify Email", "context": "text_widgets"}, "text_check_your_inbox": "• Check your inbox first", "@text_check_your_inbox": {"description": "Text from text_widgets: • Check your inbox first", "context": "text_widgets"}, "text_if_not_found": "• If not found, check spam/junk folder", "@text_if_not_found": {"description": "Text from text_widgets: • If not found, check spam/junk folder", "context": "text_widgets"}, "text_i_understand": "I Understand", "@text_i_understand": {"description": "Text from text_widgets: I Understand", "context": "text_widgets"}, "text_nonac": "NONAC", "@text_nonac": {"description": "Text from text_widgets: NONAC", "context": "text_widgets"}, "form_add_your_feedback": "Add your feedback here...", "@form_add_your_feedback": {"description": "Text from form_labels: Add your feedback here...", "context": "form_labels"}, "form_task_status": "Task Status *", "@form_task_status": {"description": "Text from form_labels: Task Status *", "context": "form_labels"}, "form_search_train_number_1": "Search train number", "@form_search_train_number_1": {"description": "Text from form_labels: Search train number", "context": "form_labels"}, "form_train_number_1": "Train Number *", "@form_train_number_1": {"description": "Text from form_labels: Train Number *", "context": "form_labels"}, "form_pnr_number": "PNR Number *", "@form_pnr_number": {"description": "Text from form_labels: PNR Number *", "context": "form_labels"}, "form_passenger_name": "Passenger Name *", "@form_passenger_name": {"description": "Text from form_labels: Passenger Name *", "context": "form_labels"}, "form_coach_no": "Coach No *", "@form_coach_no": {"description": "Text from form_labels: Coach No *", "context": "form_labels"}, "form_berth_no": "Berth No *", "@form_berth_no": {"description": "Text from form_labels: <PERSON>h No *", "context": "form_labels"}, "str_submitting_feedback": "submitting <PERSON><PERSON><PERSON>", "@str_submitting_feedback": {"description": "Text from general_strings: submitting <PERSON><PERSON>back", "context": "general_strings"}, "str_passenger_feedback": "Passenger Feedback", "@str_passenger_feedback": {"description": "Text from general_strings: Passenger Feedback", "context": "general_strings"}, "text_select": "Select", "@text_select": {"description": "Text from text_widgets: Select", "context": "text_widgets"}, "form_issue_type": "Issue Type", "@form_issue_type": {"description": "Text from form_labels: Issue Type", "context": "form_labels"}, "form_sub_issue_type": "Sub Issue Type", "@form_sub_issue_type": {"description": "Text from form_labels: Sub Issue Type", "context": "form_labels"}, "form_resolved_yesno": "Resolved (Yes/No) *", "@form_resolved_yesno": {"description": "Text from form_labels: Resolved (Yes/No) *", "context": "form_labels"}, "form_crn_number": "CRN Number*", "@form_crn_number": {"description": "Text from form_labels: CRN Number*", "context": "form_labels"}, "form_train_no": "Train No *", "@form_train_no": {"description": "Text from form_labels: Train No *", "context": "form_labels"}, "form_train_name": "Train Name *", "@form_train_name": {"description": "Text from form_labels: Train Name *", "context": "form_labels"}, "form_marks_1_to": "Marks (1 to 10) *", "@form_marks_1_to": {"description": "Text from form_labels: Marks (1 to 10) *", "context": "form_labels"}, "form_remarks_by_passenger": "Remarks by Passenger", "@form_remarks_by_passenger": {"description": "Text from form_labels: Remarks by Passenger", "context": "form_labels"}, "str_railmadad_feedback": "Railmadad <PERSON>", "@str_railmadad_feedback": {"description": "Text from general_strings: Railmadad Feedback", "context": "general_strings"}, "str_train_no_is": "Train No is required", "@str_train_no_is": {"description": "Text from general_strings: Train No is required", "context": "general_strings"}, "text_failed_to_load": "Failed to load image", "@text_failed_to_load": {"description": "Text from text_widgets: Failed to load image", "context": "text_widgets"}, "text_review_feedback": "Review Feedback", "@text_review_feedback": {"description": "Text from text_widgets: Review Feedback", "context": "text_widgets"}, "form_passenger_name_1": "Passenger Name", "@form_passenger_name_1": {"description": "Text from form_labels: Passenger Name", "context": "form_labels"}, "form_crn_number_1": "CRN Number", "@form_crn_number_1": {"description": "Text from form_labels: CRN Number", "context": "form_labels"}, "form_coach_no_1": "Coach No", "@form_coach_no_1": {"description": "Text from form_labels: Coach No", "context": "form_labels"}, "form_remarks": "Remarks", "@form_remarks": {"description": "Text from form_labels: Remarks", "context": "form_labels"}, "form_task_status_1": "Task Status", "@form_task_status_1": {"description": "Text from form_labels: Task Status", "context": "form_labels"}, "str_pnrno": "pnr_no", "@str_pnrno": {"description": "Text from general_strings: pnr_no", "context": "general_strings"}, "str_crnno": "crn_no", "@str_crnno": {"description": "Text from general_strings: crn_no", "context": "general_strings"}, "str_mobileno": "mobile_no", "@str_mobileno": {"description": "Text from general_strings: mobile_no", "context": "general_strings"}, "str_feedback_media": "Feedback Media:", "@str_feedback_media": {"description": "Text from general_strings: Feedback Media:", "context": "general_strings"}, "str_updating_feedback": "Updating feedback...", "@str_updating_feedback": {"description": "Text from general_strings: Updating feedback...", "context": "general_strings"}, "str_none_selected": "None selected", "@str_none_selected": {"description": "Text from general_strings: None selected", "context": "general_strings"}, "text_deleting_feedback": "Deleting feedback...", "@text_deleting_feedback": {"description": "Text from text_widgets: Deleting feedback...", "context": "text_widgets"}, "text_feedback_deleted_successfully": "Feedback deleted successfully", "@text_feedback_deleted_successfully": {"description": "Text from text_widgets: Feedback deleted successfully", "context": "text_widgets"}, "text_error_deleting_feedback": "Error deleting feedback: $e", "@text_error_deleting_feedback": {"description": "Text from text_widgets: Error deleting feedback: $e", "context": "text_widgets"}, "text_no_feedback_available": "No feedback available for this train.", "@text_no_feedback_available": {"description": "Text from text_widgets: No feedback available for this train.", "context": "text_widgets"}, "text_train_no_trainnumber": "Train No: $trainNumber", "@text_train_no_trainnumber": {"description": "Text from text_widgets: Train No: $trainNumber", "context": "text_widgets"}, "str_feedbacks": "feedbacks", "@str_feedbacks": {"description": "Text from general_strings: feedbacks", "context": "general_strings"}, "text_non_ac": "Non AC", "@text_non_ac": {"description": "Text from text_widgets: Non AC", "context": "text_widgets"}, "form_feedback": "<PERSON><PERSON><PERSON>", "@form_feedback": {"description": "Text from form_labels: Feedback", "context": "form_labels"}, "str_errors": "errors", "@str_errors": {"description": "Text from general_strings: errors", "context": "general_strings"}, "text_message": "Message", "@text_message": {"description": "Text from text_widgets: Message", "context": "text_widgets"}, "text_job_chart_status": "Job Chart Status Added", "@text_job_chart_status": {"description": "Text from text_widgets: Job Chart Status Added", "context": "text_widgets"}, "str_loading": "Loading...", "@str_loading": {"description": "Text from general_strings: Loading...", "context": "general_strings"}, "str_loading_coaches": "Loading coaches...", "@str_loading_coaches": {"description": "Text from general_strings: Loading coaches...", "context": "general_strings"}, "str_loading_users": "Loading users...", "@str_loading_users": {"description": "Text from general_strings: Loading users...", "context": "general_strings"}, "str_update_both_trains": "Update Both Trains", "@str_update_both_trains": {"description": "Text from general_strings: Update Both Trains", "context": "general_strings"}, "str_update_job_chart": "Update Job Chart...", "@str_update_job_chart": {"description": "Text from general_strings: Update Job Chart...", "context": "general_strings"}, "text_please_select_all": "Please select all fields before submitting.", "@text_please_select_all": {"description": "Text from text_widgets: Please select all fields before submitting.", "context": "text_widgets"}, "text_update_amount_for": "Update Amount for $userId", "@text_update_amount_for": {"description": "Text from text_widgets: Update Amount for $userId", "context": "text_widgets"}, "text_assigned": "Assigned", "@text_assigned": {"description": "Text from text_widgets: Assigned", "context": "text_widgets"}, "text_amount": "Amount", "@text_amount": {"description": "Text from text_widgets: Amount", "context": "text_widgets"}, "form_amount_in_hand": "Amount in Hand (₹)", "@form_amount_in_hand": {"description": "Text from form_labels: Amount in Hand (₹)", "context": "form_labels"}, "form_select_user": "Select User", "@form_select_user": {"description": "Text from form_labels: Select User", "context": "form_labels"}, "str_isloginbefore": "isLoginBefore", "@str_isloginbefore": {"description": "Text from general_strings: isLoginBefore", "context": "general_strings"}, "str_deleted_username": "deleted $username", "@str_deleted_username": {"description": "Text from general_strings: deleted $username", "context": "general_strings"}, "str_obhsusercoach": "obhs-user-$coach", "@str_obhsusercoach": {"description": "Text from general_strings: obhs-user-$coach", "context": "general_strings"}, "text_no_image_url": "No image URL provided", "@text_no_image_url": {"description": "Text from text_widgets: No image URL provided", "context": "text_widgets"}, "text_image_downloaded_successfully": "Image downloaded successfully!", "@text_image_downloaded_successfully": {"description": "Text from text_widgets: Image downloaded successfully!", "context": "text_widgets"}, "text_failed_to_download": "Failed to download image", "@text_failed_to_download": {"description": "Text from text_widgets: Failed to download image", "context": "text_widgets"}, "text_failed_to_download_1": "Failed to download image: $error", "@text_failed_to_download_1": {"description": "Text from text_widgets: Failed to download image: $error", "context": "text_widgets"}, "text_download_image": "Download Image", "@text_download_image": {"description": "Text from text_widgets: Download Image", "context": "text_widgets"}, "text_train_trainnumber_details": "Train $trainNumber details deleted successfully", "@text_train_trainnumber_details": {"description": "Text from text_widgets: Train $trainNumber details deleted successfully", "context": "text_widgets"}, "str_coach_attendant": "coach attendant", "@str_coach_attendant": {"description": "Text from general_strings: coach attendant", "context": "general_strings"}, "str_add_train": "Add Train", "@str_add_train": {"description": "Text from general_strings: Add Train", "context": "general_strings"}, "str_recent_train_details": "Recent Train Details", "@str_recent_train_details": {"description": "Text from general_strings: Recent Train Details", "context": "general_strings"}, "str_older_train_details": "Older Train Details", "@str_older_train_details": {"description": "Text from general_strings: Older Train Details", "context": "general_strings"}, "str_edit_profile": "Edit Profile", "@str_edit_profile": {"description": "Text from general_strings: Edit Profile", "context": "general_strings"}, "text_confirm_deactivation": "Confirm Deactivation", "@text_confirm_deactivation": {"description": "Text from text_widgets: Confirm Deactivation", "context": "text_widgets"}, "text_proceed": "Proceed", "@text_proceed": {"description": "Text from text_widgets: Proceed", "context": "text_widgets"}, "text_add_email": "Add <PERSON>", "@text_add_email": {"description": "Text from text_widgets: Add Email", "context": "text_widgets"}, "text_back": "Back", "@text_back": {"description": "Text from text_widgets: Back", "context": "text_widgets"}, "text_email_verification": "Email Verification", "@text_email_verification": {"description": "Text from text_widgets: Email Verification", "context": "text_widgets"}, "text_phone_verification": "Phone Verification", "@text_phone_verification": {"description": "Text from text_widgets: Phone Verification", "context": "text_widgets"}, "text_logout_confirmation": "Logout Confirmation", "@text_logout_confirmation": {"description": "Text from text_widgets: Logout Confirmation", "context": "text_widgets"}, "text_do_you_want": "Do you want to logout now?", "@text_do_you_want": {"description": "Text from text_widgets: Do you want to logout now?", "context": "text_widgets"}, "form_enter_email_otp": "Enter Email OTP", "@form_enter_email_otp": {"description": "Text from form_labels: Enter Email OTP", "context": "form_labels"}, "form_enter_phone_otp": "Enter Phone OTP", "@form_enter_phone_otp": {"description": "Text from form_labels: Enter Phone OTP", "context": "form_labels"}, "str_not_valid": "not valid", "@str_not_valid": {"description": "Text from general_strings: not valid", "context": "general_strings"}, "str_update_profile": "Update Profile", "@str_update_profile": {"description": "Text from general_strings: Update Profile", "context": "general_strings"}, "str_inside_train_1": "Inside Train", "@str_inside_train_1": {"description": "Text from general_strings: Inside Train", "context": "general_strings"}, "text_addupdate": "Add/Update", "@text_addupdate": {"description": "Text from text_widgets: Add/Update", "context": "text_widgets"}, "form_select_coaches_optional": "Select Coaches (optional)", "@form_select_coaches_optional": {"description": "Text from form_labels: Select Coaches (optional)", "context": "form_labels"}, "str_no_train_selected": "No train selected", "@str_no_train_selected": {"description": "Text from general_strings: No train selected", "context": "general_strings"}, "str_no_date_selected": "No date selected", "@str_no_date_selected": {"description": "Text from general_strings: No date selected", "context": "general_strings"}, "text_alert": "<PERSON><PERSON>", "@text_alert": {"description": "Text from text_widgets: <PERSON><PERSON>", "context": "text_widgets"}, "text_generate_otp": "Generate OTP", "@text_generate_otp": {"description": "Text from text_widgets: Generate OTP", "context": "text_widgets"}, "str_message_1": "$message", "@str_message_1": {"description": "Text from general_strings: $message", "context": "general_strings"}, "text_otp_sent_successfully": "OTP sent successfully!", "@text_otp_sent_successfully": {"description": "Text from text_widgets: OTP sent successfully!", "context": "text_widgets"}, "text_failed_to_send": "Failed to send OTP: $e", "@text_failed_to_send": {"description": "Text from text_widgets: Failed to send OTP: $e", "context": "text_widgets"}, "text_email_saved_successfully": "<PERSON><PERSON> saved successfully!", "@text_email_saved_successfully": {"description": "Text from text_widgets: Email saved successfully!", "context": "text_widgets"}, "text_failed_to_verify": "Failed to verify OTP: $e", "@text_failed_to_verify": {"description": "Text from text_widgets: Failed to verify OTP: $e", "context": "text_widgets"}, "text_send_mobile_otp": "Send Mobile OTP", "@text_send_mobile_otp": {"description": "Text from text_widgets: Send Mobile OTP", "context": "text_widgets"}, "text_send_email_otp": "Send Email OTP", "@text_send_email_otp": {"description": "Text from text_widgets: Send Email OTP", "context": "text_widgets"}, "str_confirm_new_password": "Confirm New Password", "@str_confirm_new_password": {"description": "Text from general_strings: Confirm New Password", "context": "general_strings"}, "str_loading_stations": "Loading stations...", "@str_loading_stations": {"description": "Text from general_strings: Loading stations...", "context": "general_strings"}, "str_refreshing_data": "Refreshing data...", "@str_refreshing_data": {"description": "Text from general_strings: Refreshing data...", "context": "general_strings"}, "text_station_code_stationcode": "Station Code: ${stationCode ?? ", "@text_station_code_stationcode": {"description": "Text from text_widgets: Station Code: ${stationCode ?? ", "context": "text_widgets"}, "text_distance_distance": "Distance: ${distance ?? ", "@text_distance_distance": {"description": "Text from text_widgets: Distance: ${distance ?? ", "context": "text_widgets"}, "text_km": "} km", "@text_km": {"description": "Text from text_widgets: } km", "context": "text_widgets"}, "text_delete_confirmation": "Delete Confirmation", "@text_delete_confirmation": {"description": "Text from text_widgets: Delete Confirmation", "context": "text_widgets"}, "text_save": "Save", "@text_save": {"description": "Text from text_widgets: Save", "context": "text_widgets"}, "str_no_days_data": "No days data", "@str_no_days_data": {"description": "Text from general_strings: No days data", "context": "general_strings"}, "str_details_1": "Details", "@str_details_1": {"description": "Text from general_strings: Details", "context": "general_strings"}, "str_causercoach": "ca-user-$coach", "@str_causercoach": {"description": "Text from general_strings: ca-user-$coach", "context": "general_strings"}, "text_image_upload_initiated": "Image upload initiated successfully", "@text_image_upload_initiated": {"description": "Text from text_widgets: Image upload initiated successfully", "context": "text_widgets"}, "text_please_select_an_1": "Please select an image to upload", "@text_please_select_an_1": {"description": "Text from text_widgets: Please select an image to upload", "context": "text_widgets"}, "text_jobchart_deleted_successfully": "Job<PERSON>hart deleted successfully", "@text_jobchart_deleted_successfully": {"description": "Text from text_widgets: <PERSON><PERSON><PERSON> deleted successfully", "context": "text_widgets"}, "text_failed_to_delete_1": "Failed to delete JobChart", "@text_failed_to_delete_1": {"description": "Text from text_widgets: Failed to delete JobChart", "context": "text_widgets"}, "text_pick_image": "Pick Image", "@text_pick_image": {"description": "Text from text_widgets: Pick Image", "context": "text_widgets"}, "str_no_image_selected": "No image selected", "@str_no_image_selected": {"description": "Text from general_strings: No image selected", "context": "general_strings"}, "str_file_does_not": "File does not exist", "@str_file_does_not": {"description": "Text from general_strings: File does not exist", "context": "general_strings"}, "str_bearer_token": "Bearer $token", "@str_bearer_token": {"description": "Text from general_strings: Bearer $token", "context": "general_strings"}, "str_useridbeingupdated": "userIdBeingUpdated", "@str_useridbeingupdated": {"description": "Text from general_strings: userIdBeingUpdated", "context": "general_strings"}, "str_detailed_error_e": "Detailed error: $e", "@str_detailed_error_e": {"description": "Text from general_strings: Detailed error: $e", "context": "general_strings"}, "str_authtoken_1": "auth_token", "@str_authtoken_1": {"description": "Text from general_strings: auth_token", "context": "general_strings"}, "str_added_successfully": "added successfully", "@str_added_successfully": {"description": "Text from general_strings: added successfully", "context": "general_strings"}, "str_added_successfully_1": "Added successfully", "@str_added_successfully_1": {"description": "Text from general_strings: Added successfully", "context": "general_strings"}, "str_unexpected_error_e": "Unexpected error: $e", "@str_unexpected_error_e": {"description": "Text from general_strings: Unexpected error: $e", "context": "general_strings"}, "str_download_failed_e": "Download failed: $e", "@str_download_failed_e": {"description": "Text from general_strings: Download failed: $e", "context": "general_strings"}, "str_unknown_train": "Unknown Train", "@str_unknown_train": {"description": "Text from general_strings: Unknown Train", "context": "general_strings"}, "str_deleted_sucessfully": "deleted sucessfully", "@str_deleted_sucessfully": {"description": "Text from general_strings: deleted sucessfully", "context": "general_strings"}, "str_fetching_report": "fetching report", "@str_fetching_report": {"description": "Text from general_strings: fetching report", "context": "general_strings"}, "str_error_adding_issues": "error adding issues", "@str_error_adding_issues": {"description": "Text from general_strings: error adding issues", "context": "general_strings"}, "str_reportid": "report_id", "@str_reportid": {"description": "Text from general_strings: report_id", "context": "general_strings"}, "str_reportfor": "report_for", "@str_reportfor": {"description": "Text from general_strings: report_for", "context": "general_strings"}, "str_updated_successfully": "Updated successfully", "@str_updated_successfully": {"description": "Text from general_strings: Updated successfully", "context": "general_strings"}, "text_failed_to_load_1": "Failed to load train numbers: $e", "@text_failed_to_load_1": {"description": "Text from text_widgets: Failed to load train numbers: $e", "context": "text_widgets"}, "str_profile": "Profile", "@str_profile": {"description": "Text from general_strings: Profile", "context": "general_strings"}, "text_language": "Language", "@text_language": {"description": "Text from text_widgets: Language", "context": "text_widgets"}, "text_select_language": "Select Language", "@text_select_language": {"description": "Text from text_widgets: Select Language", "context": "text_widgets"}, "text_train_tracker": "Train Tracker", "@text_train_tracker": {"description": "Text from text_widgets: Train Tracker", "context": "text_widgets"}, "text_assign_cs": "Assign <PERSON>", "@text_assign_cs": {"description": "Text from text_widgets: Assign CS", "context": "text_widgets"}, "text_pnr_details": "PNR Details", "@text_pnr_details": {"description": "Text from text_widgets: PNR Details", "context": "text_widgets"}, "text_configuration": "Configuration", "@text_configuration": {"description": "Text from text_widgets: Configuration", "context": "text_widgets"}, "text_obhs_to_mcc": "OBHS to MCC Handover", "@text_obhs_to_mcc": {"description": "Text from text_widgets: OBHS to MCC Handover", "context": "text_widgets"}, "text_mcc_to_obhs": "MCC to OBHS Handover", "@text_mcc_to_obhs": {"description": "Text from text_widgets: MCC to OBHS Handover", "context": "text_widgets"}, "text_upload_data": "Upload data", "@text_upload_data": {"description": "Text from text_widgets: Upload data", "context": "text_widgets"}, "text_issue_management": "Issue Management", "@text_issue_management": {"description": "Text from text_widgets: Issue Management", "context": "text_widgets"}, "text_rail_sathi_qr": "Rail Sathi Qr", "@text_rail_sathi_qr": {"description": "Text from text_widgets: Rail Sathi Qr", "context": "text_widgets"}, "str_contactor_admin": "contactor admin", "@str_contactor_admin": {"description": "Text from general_strings: contactor admin", "context": "general_strings"}}